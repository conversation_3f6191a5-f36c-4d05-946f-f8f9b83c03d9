import logging

from core.base import *
from core.polyBiz import *
from core.order import *
from core.polyWebSpider import *
from core.repository import *
from utils.randomMaker import *

# 聚合业务处理服务
class polyService:

    def __init__(self):
        # 仓储
        self.repo = recordRepository()
        # 聚合页面解析器
        self.polySpider = polyWebSpider()

    def doBusiness(self, postvars):
        # 聚合业务业务参数检查
        if not postvars.get('method') or not postvars.get('platid') or not postvars.get('bizcontent'):
            return '{\"code\":\"40000\",\"messagefrom\":\"POLY\",\"msg\":\"Logic Error\",\"polyapirequestid\":\"0\",\"polyapitotalms\":0,\"subcode\":\"LXO.REQUEST_FAILURE.LOGICERROR\",\"submessage\":\"参数“platid”、“method”、“token”、“bizcontent”不能为空\"}'
        
        # 请求方法
        method = postvars['method']
        # 参数检查完成，处理对应业务
        platid = postvars['platid']
        # 会员名
        user = postvars.get('outusername', '')
        # 获取业务处理器
        processor = polyProcessorFactory.getProcessor(method, postvars)
        # 生成请求id
        requestid = randomMaker.makeRequestId()
        # 获取结果
        bizResponse = processor.getResult(requestid)

        # 随机休眠
        if postvars.get('sleep'):
            sleepMinute = random.randint(100, 3000)
            sleepSecond = sleepMinute / 1000
            time.sleep(sleepSecond)
            bizResponse.msg = ('{}(随机休眠{}秒)').format(bizResponse.msg, sleepSecond)

        jsonBiz = jsonHelper.toJson(bizResponse)

        # 请求记录入库
        self.repo.insert(requestid, platid, user, postvars.get('token'), method, jsonHelper.toJson(postvars), jsonBiz.replace('%', ''))

        return jsonBiz
    
    def getApiAll(self):
        all = self.polySpider.getApiAll()
        return jsonHelper.toJson(all)
    
    def getApiByType(self, reqVars):
        all = self.polySpider.getApiAll()

        retList = []
        notify = next(filter(lambda x: x.type == "Notify", all), None)
        if notify and notify.apitypes:
            retList = notify.apitypes

        count = len(retList)
        pageSize = int(reqVars.get('limit')) if reqVars.get('limit') else 20
        if count > pageSize and reqVars.get('page'):
            to_index = int(reqVars['page']) * pageSize
            from_index = to_index - pageSize
            retList = retList[from_index:to_index]

        return jsonHelper.toJson({'code': 0, 'msg': '', 'count': count, 'data': retList})
    
    def getApiDetail(self, reqVars):
        
        method = reqVars['id']
        response = self.polySpider.getApiRequestExample(method)

        return jsonHelper.toJson({'code': 0, 'msg': '', 'data': { 'content': response}})
    

# 订单业务处理服务
class orderService:

    def __init__(self):
        # 仓储
        self.repo = orderExampleRepository()

    def getExample(self, postvars):
        retList, count = self.repo.query(postvars)
        return jsonHelper.toJson({'code': 0, 'msg': '', 'count': count, 'data': retList})

    def addExample(self, postvars):
        return self.repo.insert(postvars['plat'], postvars['content'], postvars['tag'], postvars['priority'], postvars['remote_addr'])

    def updateExample(self, postvars):
        return self.repo.update(postvars['id'], postvars['content'], postvars['tag'], postvars['priority'], postvars['remote_addr'])

    def delExample(self, postvars):
        return self.repo.delete(postvars['id'])
    
    def getById(self, postvars):
        retList = self.repo.getOneDetail(postvars['id'])
        return jsonHelper.toJson({'code': 0, 'msg': '', 'data': retList})

    def pushByExample(self, postvars):
        # 根据订单id查询订单，生成报文后推送
        orderId = postvars['orderid']
        order = self.repo.getOne(orderId)
        if order == '':
            return jsonHelper.toJson({'code': 400, 'msg': '查询订单出错', 'success': False})

        orderObj = jsonHelper.deJson(order.replace('\xa0', ' '))

        fix_ime = postvars.get('fixtime', '') == 'true'

        # 基础数据修正
        orderUtils.baseProcess(orderObj, postvars['platid'], fix_ime)
        
        # 单号
        if postvars.get('orderno'):
            orderObj.platorderno = postvars['orderno']
            
        # 状态
        if postvars.get('orderstatus'):
            orderObj.tradestatus = postvars['orderstatus']
        else :
            orderObj.tradestatus = 'JH_02'

        pushToOpenApi = postvars.get('esapi', '') == 'true'

        ret = None
        if pushToOpenApi:
            pushHost = postvars['pushhost']
            if pushHost == '127.0.0.1' or pushHost.lower() == 'localhost':
                pushHost = postvars['remote_addr']
            pushPort = postvars['pushport']
            pushUrl = postvars['pushurl']
            # 推送地址拼接
            pushUrl = f'http://{pushHost}:{pushPort}{pushUrl}'
            fillUp = postvars.get('fillup', '') == 'true'
            ret = orderUtils.pushToOpenApi(pushUrl, postvars['appkey'], postvars['platid'], postvars['user'], postvars['token'], orderObj, fillUp)
        else:
            ret = orderUtils.pushOrder(postvars['user'], postvars['push'], postvars['platid'], postvars['shopid'], orderObj)

        logging.info('推送订单号：{} \n '.format(orderObj.platorderno))

        return ret

    def pushByExampleMany(self, postvars):
        # 根据订单id查询订单，生成报文后推送
        orderId = postvars['orderid']
        order = self.repo.getOne(orderId)
        if order == '':
            return jsonHelper.toJson({'code': 400, 'msg': '查询订单出错', 'success': False})

        count = postvars['count']
        if len(count) == 0 or int(count) < 1:
            return jsonHelper.toJson({'code': 400, 'msg': '推单数量错误', 'success': False})

        orderObj = jsonHelper.deJson(order.replace('\xa0', ' '))

        fix_ime = postvars.get('fixtime', '') == 'true'

        # 基础数据修正
        orderUtils.baseProcess(orderObj, postvars['platid'], fix_ime)
            
        # 状态
        if postvars.get('orderstatus'):
            orderObj.tradestatus = postvars['orderstatus']
        else :
            orderObj.tradestatus = 'JH_02'
            
        all = []

        for i in range(0, int(count)):
            orderObj.platorderno = randomMaker.makeRandomNo()
            orderUtils.pushOrder(postvars['user'], postvars['push'], postvars['platid'], postvars['shopid'], orderObj)
            logging.info('推送订单号：{} \n '.format(orderObj.platorderno))
            all.append(orderObj.platorderno)

        return jsonHelper.toJson(all)

    def makeOrder(self, postvars):
        if not postvars.get('goods'):
            return jsonHelper().toJson({'code': 400, 'msg': '商品不能为空', 'success': False})
        if not postvars.get('shopid'):
            return jsonHelper().toJson({'code': 400, 'msg': '店铺id不能为空', 'success': False})
        if not postvars.get('plat'):
            return jsonHelper().toJson({'code': 400, 'msg': '平台值不能为空', 'success': False})
        if not postvars.get('member_name'):
            return jsonHelper().toJson({'code': 400, 'msg': '吉客号不能为空', 'success': False})
        if not postvars.get('push'):
            return jsonHelper().toJson({'code': 400, 'msg': '推送方式不能为空', 'success': False})
    
        goods = jsonHelper.deJson(postvars['goods'])
        # 获取订单业务处理器
        processor = polyProcessorFactory.getProcessor('Differ.JH.Business.GetOrder', postvars['plat'], '', '', postvars['member_name'], '')
        # 生成请求id
        requestid = randomMaker.makeRequestId()
        # 获取结果
        ret = processor.getResult(requestid)
        # 解析，根据入参赋值
        retObj = jsonHelper.deJson(ret)
        order = retObj.orders[0]
        # 默认JH_02
        order.tradestatus = 'JH_02'
        # 根据商品入参生成商品
        order.goodinfos = orderUtils.makeOrderGoods(order, goods)     
    
        # 执行推送
        ret = orderUtils.pushOrder(postvars['member_name'], postvars['push'], postvars['plat'], postvars['shopid'], order)
        return ret

    def pushNotice(self, postvars):

        memberName = postvars['user']
        platId = postvars['platid']
        shopId = postvars['shopid']
        shopToken = postvars['shoptoken']
        messageType = postvars['messagetype']
        msg = postvars['msgcontent']

        ret = orderUtils.pushNotice(memberName, platId, shopId, shopToken, messageType, msg)

        return ret

# 商品业务处理服务
class goodsService:
    
    def __init__(self):
        # 仓储
        self.repo = goodsRepository()

    def getGoodsBrief(self, postvars):
        retList, count = self.repo.getGoods(postvars, False)
        return jsonHelper.toJson({'code': 0, 'msg': '', 'count': count, 'data': retList})
    
    def getById(self, postvars):
        entityId = postvars['id']
        retList = self.repo.getOne(entityId) if entityId else []
        return jsonHelper.toJson({'code': 0, 'msg': '', 'data': retList})

    def addGoods(self, postvars):
        return self.repo.insert(postvars['plat'], postvars['content'], postvars['tag'], postvars['remote_addr'])

    def updateGoods(self, postvars):
        return self.repo.update(postvars['id'], postvars['content'], postvars['tag'], postvars['remote_addr'])

    def delGoods(self, postvars):
        if not postvars.get('id'):
            return '参数id为空'
        return self.repo.delGoods(postvars['id'])

# 店铺服务
class shopService:

    def getShopList(self, postvars):
        user = postvars['user']
        plat = postvars['plat']
        auth_shop_only = 'auth_shop_only' in postvars.keys() and postvars['auth_shop_only'] == 'true'
        retList = shopRepository(user).getShopByPlat(plat, auth_shop_only)
        # long转string
        for item in retList:
            item.shop_id = str(item.shop_id)
        return jsonHelper.toJson(retList)

# 请求记录服务
class recordService:
    
    def __init__(self):
        # 仓储
        self.repo = recordRepository()

    def getRecords(self, postvars):
        retList, count = self.repo.getRecord(postvars)
        return jsonHelper.toJson({'code': 0, 'msg': '', 'count': count, 'data': retList})

    def getRecordDetail(self, postvars):
        if not postvars.get('reqid'):
            return '参数reqId为空'
        
        reqId = postvars['reqid']
        detailObj = self.repo.getRecordDetail(reqId)
        return jsonHelper.toJson(detailObj)
    
    def getRecordsByMaxtimeId(self, maxTime, maxCount):
        return self.repo.getRecordsByMaxtimeId(maxTime, maxCount)

    def delRecordsByRequestId(self, request_id_list):
        return self.repo.delRecordsByRequestId(request_id_list)
    
    def delRecordsDetailByRequestId(self, request_id_list):
        return self.repo.delRecordsDetailByRequestId(request_id_list)

# 通用样例服务
class commonExampleService:

    def __init__(self):
        self.repo = commonExampleRepository()

    def getExample(self, postvars):
        retList, count = self.repo.getExamples(postvars)
        return jsonHelper.toJson({'code': 0, 'msg': '', 'count': count, 'data': retList})
    
    def getById(self, postvars):
        entityId = postvars['id']
        retList = self.repo.getOne(entityId) if entityId else []

        # 为每个返回的记录添加 mappingId 字段
        if retList:
            from core.repository import polyMethodConfigRepository
            poly_config_repo = polyMethodConfigRepository()

            for item in retList:
                # 初始化 mappingId 和 mappingFeild 为空
                item.mappingId = ''
                item.mappingFeild = ''

                # 当 biz_feild 字段值不为空时，查询 mappingId 和 mappingFeild
                if hasattr(item, 'biz_feild') and item.biz_feild and hasattr(item, 'method') and item.method:
                    try:
                        # 根据 method 和 biz_feild（作为return_feild）查询配置
                        configs = poly_config_repo.getByMethodAndReturnField(item.method, item.biz_feild)
                        if configs and len(configs) > 0:
                            # 取第一个匹配的配置的 id 和 req_feild
                            item.mappingId = str(configs[0].id)
                            item.mappingFeild = configs[0].req_feild if hasattr(configs[0], 'req_feild') else ''
                    except Exception as e:
                        logging.error(f'查询mappingId和mappingFeild出错：{str(e)}')
                        item.mappingId = ''
                        item.mappingFeild = ''

        return jsonHelper.toJson({'code': 0, 'msg': '', 'data': retList})

    def addExample(self, postvars):
        return self.repo.insert(postvars['plat'], postvars['method'], postvars['biz_feild'], postvars['content'], postvars['tag'], postvars['remote_addr'])

    def updateExample(self, postvars):
        return self.repo.update(postvars['id'], postvars['plat'], postvars['biz_feild'], postvars['content'], postvars['tag'], postvars['remote_addr'])

    def delExample(self, postvars):
        return self.repo.delete(postvars['id'])

    def getFieldMapping(self, postvars):
        """根据mappingId查询字段映射配置"""
        mapping_id = postvars.get('mappingid')
        if not mapping_id:
            return jsonHelper.toJson({'code': 400, 'msg': 'mappingid参数不能为空', 'data': None})

        try:
            from core.repository import polyMethodConfigRepository
            poly_config_repo = polyMethodConfigRepository()

            configs = poly_config_repo.getOne(mapping_id)

            if configs and len(configs) > 0:
                config = configs[0]
                feild_map = config.feild_map if hasattr(config, 'feild_map') else ''
                return jsonHelper.toJson({'code': 0, 'msg': '', 'data': {'feild_map': feild_map}})
            else:
                return jsonHelper.toJson({'code': 404, 'msg': '未找到对应的映射配置', 'data': None})

        except Exception as e:
            logging.error(f'查询字段映射配置出错：{str(e)}')
            return jsonHelper.toJson({'code': 500, 'msg': f'查询出错：{str(e)}', 'data': None})

    def updateFieldMapping(self, postvars):
        """更新字段映射配置"""
        mapping_id = postvars.get('mappingid')
        if not mapping_id:
            return jsonHelper.toJson({'code': 400, 'msg': 'mappingid参数不能为空', 'data': None})

        feild_map = postvars.get('feild_map')
        if not feild_map:
            return jsonHelper.toJson({'code': 400, 'msg': 'feild_map参数不能为空', 'data': None})

        try:
            from core.repository import polyMethodConfigRepository
            poly_config_repo = polyMethodConfigRepository()

            # 验证JSON格式
            import json
            json.loads(feild_map)  # 验证是否为有效JSON

            # 先查询原配置
            configs = poly_config_repo.getOne(mapping_id)
            if not configs or len(configs) == 0:
                return jsonHelper.toJson({'code': 404, 'msg': '未找到对应的映射配置', 'data': None})

            config = configs[0]
            # 更新配置
            result = poly_config_repo.update(
                mapping_id,
                config.method,
                config.req_feild,
                config.return_feild,
                feild_map
            )

            return jsonHelper.toJson({'code': 0, 'msg': '更新成功', 'data': None})

        except json.JSONDecodeError:
            return jsonHelper.toJson({'code': 400, 'msg': '字段映射格式错误，必须是有效的JSON', 'data': None})
        except Exception as e:
            logging.error(f'更新字段映射配置出错：{str(e)}')
            return jsonHelper.toJson({'code': 500, 'msg': f'更新出错：{str(e)}', 'data': None})

    def addFieldMapping(self, postvars):
        """新增字段映射配置"""
        method = postvars.get('method')
        req_feild = postvars.get('req_feild')
        return_feild = postvars.get('return_feild')

        if not method:
            return jsonHelper.toJson({'code': 400, 'msg': 'method参数不能为空', 'data': None})
        if not req_feild:
            return jsonHelper.toJson({'code': 400, 'msg': 'req_feild参数不能为空', 'data': None})
        if not return_feild:
            return jsonHelper.toJson({'code': 400, 'msg': 'return_feild参数不能为空', 'data': None})

        try:
            from core.repository import polyMethodConfigRepository
            poly_config_repo = polyMethodConfigRepository()

            # 检查是否已存在相同的配置
            existing_configs = poly_config_repo.getByMethodAndFields(method, req_feild, return_feild)
            if existing_configs and len(existing_configs) > 0:
                return jsonHelper.toJson({'code': 400, 'msg': '该配置已存在', 'data': None})

            # 创建默认的字段映射
            default_feild_map = '[]'  # 空的映射数组

            # 插入新配置
            result = poly_config_repo.insert(method, req_feild, return_feild, default_feild_map)

            return jsonHelper.toJson({'code': 0, 'msg': '新增成功', 'data': None})

        except Exception as e:
            logging.error(f'新增字段映射配置出错：{str(e)}')
            return jsonHelper.toJson({'code': 500, 'msg': f'新增出错：{str(e)}', 'data': None})

# 平台服务
class platService:

    def __init__(self):
        # 仓储
        self.repo = platRepository()

    def getPlatList(self):
        retList = self.repo.getPlatList()
        # int转string、名称格式化
        for item in retList:
            item.PlatValue = str(item.PlatValue)
        return jsonHelper.toJson(retList)

    def addPlat(self, postvars):
        if not postvars.get('platval'):
            return '平台值不能为空'
        if not postvars.get('platname'):
            return '平台名称不能为空'

        platId = postvars['platval']
        platName = postvars['platname']
        return self.repo.addPlat(platId, platName)
