2025-06-13 20:33:47 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-13 20:33:47 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-13 20:33:47 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-13 20:33:47 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-13 20:33:47 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-13 20:33:47 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-13 20:33:47 | INFO  | base.py        :start                | Scheduler started
2025-06-13 20:33:47 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-13 20:33:47 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-13 20:33:52 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- (from=192.168.5.235)
2025-06-13 20:33:52 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-13 20:33:52) - 0.002s
2025-06-13 20:34:14 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- (from=192.168.5.235)
2025-06-13 20:35:33 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-13 20:34:14) - 78.809s
2025-06-13 20:56:20 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-13 20:56:21 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-13 20:56:21 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-13 20:56:21 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-13 20:56:21 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-13 20:56:21 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-13 20:56:21 | INFO  | base.py        :start                | Scheduler started
2025-06-13 20:56:21 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-13 20:56:21 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-13 20:56:25 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-13 20:56:25 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-13 20:56:25) - 0.002s
2025-06-13 20:56:37 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-13 20:56:38 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-13 20:56:38 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-13 20:56:38 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-13 20:56:38 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-13 20:56:38 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-13 20:56:38 | INFO  | base.py        :start                | Scheduler started
2025-06-13 20:56:38 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-13 20:56:38 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-13 20:57:00 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-13 20:57:01 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-13 20:57:01 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-13 20:57:01 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-13 20:57:01 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-13 20:57:01 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-13 20:57:01 | INFO  | base.py        :start                | Scheduler started
2025-06-13 20:57:01 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-13 20:57:01 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-13 20:57:06 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-13 20:57:06 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-13 20:57:06) - 0.097s
2025-06-13 20:59:26 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-13 20:59:27 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-13 20:59:27 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-13 20:59:27 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-13 20:59:27 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-13 20:59:27 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-13 20:59:27 | INFO  | base.py        :start                | Scheduler started
2025-06-13 20:59:27 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-13 20:59:27 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-13 21:00:29 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-13 21:00:30 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-13 21:00:30 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-13 21:00:30 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-13 21:00:30 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-13 21:00:30 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-13 21:00:30 | INFO  | base.py        :start                | Scheduler started
2025-06-13 21:00:30 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-13 21:00:30 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-13 21:02:03 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-13 21:02:03 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-13 21:02:03) - 0.058s
2025-06-13 21:03:31 | INFO  | main.py        :log_requests         | [POST]/openapi/do -- (from=192.168.5.235)
2025-06-13 21:03:31 | INFO  | main.py        :log_requests         | [POST]/openapi/do? -- 200 (from=192.168.5.235,2025-06-13 21:03:31) - 0.070s
