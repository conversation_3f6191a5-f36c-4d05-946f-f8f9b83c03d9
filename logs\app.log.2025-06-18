2025-06-18 23:32:38 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-18 23:32:38 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-18 23:32:38 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-18 23:32:38 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-18 23:32:38 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-18 23:32:38 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-18 23:32:38 | INFO  | base.py        :start                | Scheduler started
2025-06-18 23:32:38 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-18 23:32:38 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:32:54) - 0.126s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.037s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.033s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.035s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.035s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.045s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.030s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.019s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.023s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.021s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.022s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.024s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:32:54) - 0.027s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.015s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.018s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.018s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.023s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.022s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.017s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=192.168.5.235,2025-06-18 23:32:54) - 0.009s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.014s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.013s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:32:54) - 0.018s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:32:54) - 0.019s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 304 (from=192.168.5.235,2025-06-18 23:32:54) - 0.004s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:32:54) - 0.013s
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:32:54 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:32:54) - 0.004s
2025-06-18 23:32:58 | INFO  | main.py        :log_requests         | [POST]/goods/get -- (from=192.168.5.235)
2025-06-18 23:32:58 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=192.168.5.235,2025-06-18 23:32:58) - 0.014s
2025-06-18 23:32:58 | INFO  | main.py        :log_requests         | [POST]/goods/getById -- (from=192.168.5.235)
2025-06-18 23:32:58 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=192.168.5.235,2025-06-18 23:32:58) - 0.004s
2025-06-18 23:32:59 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType -- (from=192.168.5.235)
2025-06-18 23:32:59 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- 200 (from=192.168.5.235,2025-06-18 23:32:59) - 0.009s
2025-06-18 23:32:59 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById -- (from=192.168.5.235)
2025-06-18 23:32:59 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById? -- 200 (from=192.168.5.235,2025-06-18 23:32:59) - 0.009s
2025-06-18 23:33:01 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:33:01 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:33:01) - 0.008s
2025-06-18 23:33:01 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:33:01 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:33:01) - 0.009s
2025-06-18 23:33:09 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:33:09 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:33:09) - 0.008s
2025-06-18 23:33:25 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:33:25 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:33:25) - 0.003s
2025-06-18 23:33:25 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:33:25 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:33:25) - 0.008s
2025-06-18 23:33:32 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping -- (from=192.168.5.235)
2025-06-18 23:33:32 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping? -- 200 (from=192.168.5.235,2025-06-18 23:33:32) - 0.002s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:40:03) - 0.004s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.021s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.021s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.022s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.020s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.026s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.026s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.017s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.019s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.024s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.019s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:40:03) - 0.020s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.016s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.013s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.015s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.015s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.017s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.014s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.013s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.010s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.011s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.019s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:40:03) - 0.017s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:40:03) - 0.009s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 304 (from=192.168.5.235,2025-06-18 23:40:03) - 0.003s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:40:03) - 0.014s
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:40:03 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:40:03) - 0.004s
2025-06-18 23:40:04 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:40:04 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:40:04) - 0.008s
2025-06-18 23:40:04 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:40:04 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:40:04) - 0.004s
2025-06-18 23:40:09 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:40:09 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:40:09) - 0.008s
2025-06-18 23:40:09 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:40:10 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:40:09) - 0.005s
2025-06-18 23:40:14 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:40:14 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:40:14) - 0.005s
2025-06-18 23:40:37 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:40:37 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:40:37) - 0.004s
2025-06-18 23:40:37 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:40:37 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:40:37) - 0.005s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:41:24) - 0.002s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.005s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.014s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.013s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.013s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.015s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.016s
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-18 23:41:24 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.010s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.008s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.008s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:24) - 0.008s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:41:25) - 0.008s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.007s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.009s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.009s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.009s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.013s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.010s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.010s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.009s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.006s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.005s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:41:25) - 0.012s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:41:25) - 0.007s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 304 (from=192.168.5.235,2025-06-18 23:41:25) - 0.002s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:41:25) - 0.010s
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:41:25 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:41:25) - 0.003s
2025-06-18 23:41:26 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:41:26 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:41:26) - 0.004s
2025-06-18 23:41:26 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:41:26 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:41:26) - 0.003s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:42:11) - 0.002s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.002s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.004s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.011s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.011s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.011s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.013s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.012s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.008s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.011s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:42:11) - 0.010s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.010s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.009s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.009s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.010s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.010s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.011s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.007s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.008s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.011s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-18 23:42:11) - 0.009s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:42:11) - 0.009s
2025-06-18 23:42:11 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:42:12 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:42:11) - 0.026s
2025-06-18 23:42:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-18 23:42:12 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 304 (from=192.168.5.235,2025-06-18 23:42:12) - 0.003s
2025-06-18 23:42:12 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:42:12 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:42:12) - 0.014s
2025-06-18 23:42:12 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:42:12 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:42:12) - 0.003s
2025-06-18 23:42:14 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:42:14 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:42:14) - 0.004s
2025-06-18 23:42:14 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:42:14 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:42:14) - 0.012s
2025-06-18 23:42:26 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:42:26 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:42:26) - 0.003s
2025-06-18 23:42:26 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:42:26 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:42:26) - 0.003s
2025-06-18 23:43:05 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping -- (from=192.168.5.235)
2025-06-18 23:43:05 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping? -- 200 (from=192.168.5.235,2025-06-18 23:43:05) - 0.001s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.002s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.005s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.010s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.012s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.012s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.010s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.010s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.007s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.008s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.008s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.009s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.010s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.010s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.011s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.009s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.007s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.008s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.011s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.007s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.006s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.006s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.006s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.008s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.009s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.002s
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico -- (from=192.168.5.235)
2025-06-18 23:47:34 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- 200 (from=192.168.5.235,2025-06-18 23:47:34) - 0.002s
2025-06-18 23:47:35 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:47:35 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:47:35) - 0.008s
2025-06-18 23:47:35 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:47:35 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:47:35) - 0.003s
2025-06-18 23:47:36 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:47:36 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:47:36) - 0.004s
2025-06-18 23:47:36 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:47:36 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:47:36) - 0.003s
2025-06-18 23:47:41 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:47:41 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:47:41) - 0.004s
2025-06-18 23:47:41 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:47:41 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:47:41) - 0.006s
2025-06-18 23:48:28 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:48:28 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:48:28) - 0.004s
2025-06-18 23:48:28 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:48:28 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:48:28) - 0.003s
2025-06-18 23:48:32 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:48:32 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:48:32) - 0.011s
2025-06-18 23:48:32 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:48:32 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:48:32) - 0.004s
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:48:33) - 0.004s
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:48:33) - 0.007s
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-18 23:48:33) - 0.010s
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:48:33) - 0.019s
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:48:33) - 0.009s
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:48:33) - 0.012s
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:48:33 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:48:33) - 0.003s
2025-06-18 23:48:34 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:48:34 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:48:34) - 0.005s
2025-06-18 23:48:34 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:48:34 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:48:34) - 0.012s
2025-06-18 23:48:40 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:48:40 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:48:40) - 0.010s
2025-06-18 23:48:40 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:48:40 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:48:40) - 0.005s
2025-06-18 23:48:44 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map -- (from=192.168.5.235)
2025-06-18 23:48:44 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=192.168.5.235,2025-06-18 23:48:44) - 0.002s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.002s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.004s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.010s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.010s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.011s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.012s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.013s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.009s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.008s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.011s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.009s
2025-06-18 23:49:34 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:34) - 0.009s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.010s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.010s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.008s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.009s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.008s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.011s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.007s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.007s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.006s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.006s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=192.168.5.235,2025-06-18 23:49:35) - 0.001s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.009s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.019s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.002s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.004s
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:49:35 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:49:35) - 0.010s
2025-06-18 23:49:37 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:49:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:49:37) - 0.003s
2025-06-18 23:49:38 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:49:38 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:49:38) - 0.009s
2025-06-18 23:49:38 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:49:38 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:49:38) - 0.004s
2025-06-18 23:49:44 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:49:44 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:49:44) - 0.004s
2025-06-18 23:49:44 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:49:44 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:49:44) - 0.005s
2025-06-18 23:49:45 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:49:45 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:49:45) - 0.004s
2025-06-18 23:49:45 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:49:45 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:49:45) - 0.005s
2025-06-18 23:49:47 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map -- (from=192.168.5.235)
2025-06-18 23:49:47 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=192.168.5.235,2025-06-18 23:49:47) - 0.002s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.002s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.003s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.010s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.009s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.010s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.012s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.013s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.007s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.007s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.008s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.009s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.011s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.012s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.009s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.010s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.007s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.009s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.007s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.006s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.007s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.007s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.007s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.009s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.025s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.001s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.002s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.010s
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:54:03 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:54:03) - 0.003s
2025-06-18 23:54:04 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:54:04 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:54:04) - 0.007s
2025-06-18 23:54:04 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:54:04 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:54:04) - 0.006s
2025-06-18 23:54:09 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:54:09 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:54:09) - 0.004s
2025-06-18 23:54:09 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:54:09 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:54:09) - 0.006s
2025-06-18 23:54:22 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping -- (from=192.168.5.235)
2025-06-18 23:54:22 | INFO  | main.py        :log_requests         | [POST]/common/getFieldMapping? -- 200 (from=192.168.5.235,2025-06-18 23:54:22) - 0.002s
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-18 23:55:03) - 0.002s
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 304 (from=192.168.5.235,2025-06-18 23:55:03) - 0.004s
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 200 (from=192.168.5.235,2025-06-18 23:55:03) - 0.004s
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-18 23:55:03) - 0.007s
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-18 23:55:03) - 0.007s
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-18 23:55:03) - 0.009s
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-18 23:55:03 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-18 23:55:03) - 0.002s
2025-06-18 23:55:04 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:55:04 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:55:04) - 0.005s
2025-06-18 23:55:04 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:55:04 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:55:04) - 0.016s
2025-06-18 23:55:09 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-18 23:55:09 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-18 23:55:09) - 0.005s
2025-06-18 23:55:09 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-18 23:55:09 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-18 23:55:09) - 0.007s
2025-06-18 23:59:56 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map -- (from=192.168.5.235)
2025-06-18 23:59:56 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=192.168.5.235,2025-06-18 23:59:56) - 0.001s
