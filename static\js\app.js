// 全局变量
// 平台列表
var platAll = [{ "PlatValue": "", "Name": "" }];
// 接口列表
var apiAll = [{ "apitype": "", "name": "" }];
// 用户平台店铺
var userPlatShop = [];

// 表格高度
var tableHeight = Math.max(document.documentElement.scrollHeight, document.documentElement.clientHeight) - 60;
var tablineHeight = parseInt(tableHeight / 40 / 5) * 5;

// 各菜单配置
var menuConfig = {};

// 重新加载会员+平台店铺
function reloadUserPlatShop(platId, userName, shopSelectId) {
  userPlatShop = []
  $("#" + shopSelectId).empty();
  if (!platId || platId == '') {
    return
  }

  var auth_shop_only = $('#auth-shop-only').prop('checked');

  $.ajax({
    url: "/shop/getShopByPlat",
    type: 'POST',
    async: false,
    data: { user: userName, plat: platId, auth_shop_only: auth_shop_only },
    dataType: "text",
    success: function (ret) {
      var data = JSON.parse(ret)
      if (data.length > 0) {
        userPlatShop = userPlatShop.concat(data);

        $.each(data, function (i, item) {
          var options = "<option value='" + item.shop_id + "'>" + item.shop_name + "</option>";
          $("#" + shopSelectId).append(options);
        });

        // 重新渲染表单
        layui.form.render();
      }
    },
    error: function (ex) {
      layer.msg('店铺加载失败：' + ex);
    }
  });
}

// 格式化表格数据中的plat值
function formatRowDataPlat(rowData) {
  if (!rowData) {
    return;
  }
  rowData.platId = rowData.plat;
  if (rowData.plat == '' || rowData.plat == 0) {
    rowData.plat = '【通用】'
  } else {
    var match = platAll.find(a => a.PlatValue == rowData.plat)
    if (match) {
      rowData.plat = match.Name;
    }
  }
}

// 显示字段映射对话框
function showFieldMappingDialog(fieldMapJson) {
  try {
    var fieldMappings = JSON.parse(fieldMapJson);
    if (!Array.isArray(fieldMappings)) {
      layer.msg('字段映射配置格式错误');
      return;
    }

    // 构建映射表格HTML
    var tableHtml = '<table class="layui-table" style="margin: 0;">';
    tableHtml += '<thead><tr><th style="text-align: center;">请求字段</th><th style="text-align: center;">响应字段</th></tr></thead>';
    tableHtml += '<tbody>';

    fieldMappings.forEach(function(mapping) {
      if (mapping.request && mapping.response) {
        tableHtml += '<tr>';
        tableHtml += '<td style="text-align: center;">' + mapping.request + '</td>';
        tableHtml += '<td style="text-align: center;">' + mapping.response + '</td>';
        tableHtml += '</tr>';
      }
    });

    tableHtml += '</tbody></table>';

    // 显示对话框
    layer.open({
      type: 1,
      title: '字段映射配置',
      area: ['500px', '400px'],
      content: tableHtml,
      btn: ['关闭'],
      btnAlign: 'c'
    });

  } catch (e) {
    layer.msg('解析字段映射配置失败：' + e.message);
  }
}

// 重置详情表单
function resetDetail(menu_role) {
  $("#example-id").val('');
  $('#example-plat').val('');
  $('#example-tag').val('');
  $('#example-feild').val('');
  $('#example-plat-id').val('');
  $('#example-method').val('');
  $('#example-priority').val('');
  $('#example-content').val('');
  $('#example-interface').val('');

  // 重置映射信息
  $('#mapping-info').hide();
  $('#mapping-text').text('');
  $('#mapping-config-btn').removeData('mappingId');

  if (menu_role == 'polymsg') {
    $('#preview-form > .layui-form-item').hide();
    $('#preview-form > .push-notify').show();
  } else {
    $('#preview-form > .layui-form-item').show();
    $('#preview-form > .push-notify').hide();

    if (menu_role === 'common') {
      $('#feild-edit-box').show();
      $('#priority-edit-box').hide();
    } else {
      $('#feild-edit-box').hide();
      $('#priority-edit-box').show();
    }

    if (menu_role === 'order') {
      $('#preview-form > .order-tool').show();
    } else {
      $('#preview-form > .order-tool').hide();
    }

  }

  $('#content-box').show();
}

// 格式化json
function formatJson(json_str) {
  try {
    jsonObject = jsonlint.parse(json_str);
    json = JSON.stringify(jsonObject, undefined, 4);
    return json;
  }
  catch (e) {
    console.log('格式化json出错:' + e);
    console.log(json_str);
    return json_str;
  }
}

// 获取并检查example-content内的json数据
function getExampleContent(menu_role) {
  var dataContent = $('#example-content').val();
  if (dataContent == '') {
    alertError('请不要输入空数据')
    return null;
  }

  var obj = null;
  try {
    obj = jsonlint.parse(dataContent.toLowerCase());
    if (typeof obj == 'object' && obj) {
      // 使用模块名称获取对应的模块对象
      var moduleKey = menu_role.charAt(0).toUpperCase() + menu_role.slice(1) + 'Module';
      var module = window[moduleKey];

      // 如果模块存在且有验证函数，则调用验证函数
      if (module && typeof module.validateData === 'function') {
        if (!module.validateData(obj)) {
          return null;
        }
      }
    } else {
      alertError('数据格式非object, 请检查后重新录入');
      return null;
    }
  } catch (e) {
    alertError('数据格式不正确, 请检查后重新输入');
    console.log(e);
    return null;
  }

  // 压缩
  var t = dataContent.replace(/[\r\n]/g, "")
  for (var n = [], o = !1, i = 0, r = t.length; r > i; i++) {
    var a = t.charAt(i);
    o && a === o ? "\\" !== t.charAt(i - 1) && (o = !1) : o || '"' !== a && "'" !== a ? o || " " !== a && "	" !== a || (a = "") : o = a, n.push(a)
  }
  dataContent = n.join("");
  return dataContent;
}

// layer弹出错误信息
function alertError(error_msg) {
  layer.alert(error_msg, { icon: 2, title: '错误' })
}

// 应用主题到iframe
function applyThemeToIframe(layero) {
  if (!layero) {
    return;
  }

  var layerFrame = layero.find('iframe');
  if (!layerFrame.length) {
    return;
  }

  var iframeWin = window[layero.find('iframe')[0]['name']];
  if ($('#skin-switch').prop('checked')) {
    iframeWin.$('#layui_theme_css').attr('href', './js/layui/css/layui-theme-dark.css');
  } else {
    iframeWin.$('#layui_theme_css').attr('href', '');
  }
}

// 主应用初始化
(function() {
  // 初始化应用
  function initApp() {
    // 配置layui
    layui.config({
      version: '1626897823561' //为了更新 js 缓存, 可忽略
    });

    // 加载layui模块
    layui.use(function () {
      // 得到各种内置组件
      var layer = layui.layer //弹层
        , laypage = layui.laypage //分页
        , laydate = layui.laydate //日期
        , table = layui.table //表格
        , element = layui.element //元素操作
        , dropdown = layui.dropdown //下拉菜单
        , carousel = layui.carousel //轮播
        , form = layui.form;

      // 初始化功能模块
      initModules();

      // 初始化组件
      initComponents();

      // 绑定主题切换事件
      bindThemeSwitch();

      // 加载平台和接口数据
      loadPlatAndApiData();

      // 绑定表格工具栏事件
      bindTableToolEvents();

      // 确保所有模块都已加载完成后，再触发第一个菜单的点击事件
      setTimeout(function() {
        $("li.layui-this>a").trigger("click");
      }, 100);
    });
  }

  // 初始化功能模块
  function initModules() {
    // 初始化通用模块（必须先初始化，因为其他组件依赖它）
    CommonModule.init();

    // 初始化订单模块
    OrderModule.init();

    // 初始化商品模块
    GoodsModule.init();

    // 初始化消息模块
    PolymsgModule.init();

    // 初始化日志模块
    RecordModule.init();

    // 初始化说明模块
    GuideModule.init();
  }

  // 初始化组件
  function initComponents() {
    // 初始化侧边栏
    SidebarComponent.init();

    // 初始化工具栏
    ToolbarComponent.initMainToolbar();

    // 初始化布局
    LayoutComponent.initMainLayout();
    LayoutComponent.initRecordLayout();
    LayoutComponent.initGuideLayout();
  }

  // 绑定主题切换事件
  function bindThemeSwitch() {
    layui.form.on('switch(skin-switch)', function (data) {
      var jsonPath = '';

      if ($('#skin-switch').prop('checked')) {
        // 设置为深色主题
        document.getElementById('layui_theme_css').setAttribute('href', './static/js/layui/css/layui-theme-dark.css');
        jsonPath = "https://unpkg.com/live2d-widget-model-tororo@1.0.5/assets/tororo.model.json";
      } else {
        // 恢复浅色主题
        document.getElementById('layui_theme_css').removeAttribute('href');
        jsonPath = "https://unpkg.com/live2d-widget-model-hijiki@1.0.5/assets/hijiki.model.json";
      }

      window.L2Dwidget.config.model.jsonPath = jsonPath;
      window.L2Dwidget.init();
    });
  }

  // 绑定表格工具栏事件
  function bindTableToolEvents() {
    // 绑定映射配置按钮点击事件
    $(document).on('click', '#mapping-config-btn', function() {
      var mappingId = $(this).data('mappingId');
      if (!mappingId) {
        layer.msg('映射ID为空');
        return;
      }

      // 请求字段映射配置
      $.ajax({
        url: '/common/getFieldMapping',
        type: 'POST',
        data: { mappingId: mappingId },
        dataType: 'json',
        success: function(result) {
          if (result.code === 0 && result.data && result.data.feild_map) {
            showFieldMappingDialog(result.data.feild_map);
          } else {
            layer.msg(result.msg || '获取字段映射配置失败');
          }
        },
        error: function(xhr, status, error) {
          layer.msg('请求失败：' + error);
        }
      });
    });

    // 表格工具栏事件
    layui.table.on('tool(main_tool)', function (obj) {

      var data = obj.data;
      var layEvent = obj.event;
      var menu_role = $("li.layui-this>a").data("role");

      if (layEvent === 'row_del') {
        layer.confirm('确认要删除该记录?', {
          btn: ['确认', '取消'] //按钮
        }, function () {

          var post_url = "/" + menu_role + "/del";
          $.ajax({
            url: post_url,
            type: 'POST',
            async: false,
            data: { id: data.id },
            dataType: "text",
            success: function (result) {
              if (result == 'success') {
                layer.msg('已删除', { icon: 1 });
                // 重新加载表格数据
                layui.table.reload('main-table', {
                  url: menuConfig[menu_role].url,
                  where: {
                    plat: $('#plat_choose').val() || '',
                    method: $('#method_choose').val() || ''
                  },
                  page: { curr: 1 }
                });
              } else {
                layer.msg('提交失败：' + result, { icon: 0 });
              }
            },
            error: function (ex) {
              layer.msg('提交失败：' + ex.toString(), { icon: 2 });
            }
          });

        });
      }

      if (layEvent === 'order_push') {
        layer.open({
          skin: 'layui-layer-rim',
          type: 2,
          area: ['524px', '600px'],
          title: '吉客云订单推送',
          shadeClose: true,
          content: './static/orderPush.html',
          success: function (layero, index) {
            var body = layer.getChildFrame('body', index);
            // 获取子页面的元素, 进行数据渲染
            body.contents().find("#plat").val(data.platId);
            body.contents().find('#order_Id').val(data.id);

            // 得到iframe页的窗口对象
            var iframeWin = window[layero.find('iframe')[0]['name']];
            // 加载店铺数据
            iframeWin.loadShop();

            applyThemeToIframe(layero);
          }
        });
      }
      
      if (layEvent === 'openapi_push') {
        layer.open({
          skin: 'layui-layer-rim',
          type: 2,
          area: ['550px', '660px'],
          title: '管家订单推送',
          shadeClose: true,
          content: './static/openApiOrderPush.html',
          success: function (layero, index) {
            var body = layer.getChildFrame('body', index);
            body.contents().find("#plat").val(data.platId);
            body.contents().find('#order_Id').val(data.id);

            applyThemeToIframe(layero);
          }
        });
      }

    });

    // 行点击
    layui.table.on('row(main_tool)', function (obj) {

      $(".layui-table-body tr").attr({ "style": "-webkit-tap-highlight-color" });//其他tr恢复原样
      $(obj.tr).attr({ "style": "background:#409EFF;color:#fff" });//改变当前tr颜色

      var menu_role = $("li.layui-this>a").data("role");
      // 重置详情面板
      resetDetail(menu_role);
      // 查询详情数据
      var post_url = "/" + menu_role + "/getById";
      $.ajax({
        url: post_url,
        type: 'POST',
        async: false,
        data: { id: obj.data.id },
        dataType: "json",
        success: function (result) {
          var data = null;
          if (result && result.data) {
            if (Object.prototype.toString.call(result.data) === '[object Array]') {
              if (result.data.length == 0) {
                layer.msg('查询失败：' + result);
                return;
              }
              data = result.data[0];
            } else {
              data = result.data;
            }

            if (menu_role == 'polymsg') {
              $('#example-interface').val(obj.data.apitype);
            } else {
              $('#example-plat').val(obj.data.plat);
              $("#example-id").val(data.id);
              $('#example-tag').val(data.tag);
              $('#example-feild').val(data.biz_feild);
              $('#example-plat-id').val(data.plat);
              $('#example-method').val(data.method);
              $('#example-priority').val(data.priority ?? 1);

              // 处理映射信息显示
              if (menu_role === 'common' && data.mappingId && data.mappingFeild) {
                $('#mapping-text').text(' <==> ' + data.mappingFeild);
                $('#mapping-info').show();
                $('#mapping-config-btn').data('mappingId', data.mappingId);
              } else {
                $('#mapping-info').hide();
              }
            }
            $('#example-content').val(formatJson(data.content));

            return;
          }
          layer.msg('查询失败：' + result);
          console.log(result)
        },
        error: function (ex) {
          layer.msg('查询失败：' + str(ex));
          console.log(ex)
        }
      });
    });

  }

  // 加载平台和接口数据
  function loadPlatAndApiData() {
    // 加载平台列表
    $.ajax({
      url: "/plat/getall",
      type: 'GET',
      dataType: 'json',
      async: false,
      success: function (data) {
        platAll = platAll.concat(data);
        // 填充下拉列表
        $.each(platAll, function (i, item) {
          var optText = '[' + item.PlatValue + ']' + item.Name;
          if (optText == '[]') {
            optText = ''
          }
          var options = "<option value='" + item.PlatValue + "'>" + optText + "</option>";
          $(".plat_choose").append(options);
        });

        // 重新渲染表单
        layui.form.render();

      },
      error: function (ex) {
        console.log(ex)
      }
    });

    // 加载接口列表
    $.ajax({
      url: "/common/getapi",
      type: 'GET',
      dataType: 'json',
      async: false,
      success: function (data) {
        var apiBusiness = data.find(a => a.type == "Business");
        if (apiBusiness) {
          apiAll = apiAll.concat(apiBusiness.apitypes);
        }
        var apiCommom = data.find(a => a.type == "Commom");
        if (apiCommom) {
          apiAll = apiAll.concat(apiCommom.apitypes);
        }
        if (apiAll.length > 0) {
          $.each(apiAll, function (i, item) {
            var options = "<option value='" + item.apitype + "'>" + item.name + "</option>";
            $(".method_choose").append(options);
          });
          // 重新渲染表单
          layui.form.render();
        }
      },
      error: function (ex) {
        layer.open({
          type: 1,
          area: ['512px', '384px'],
          title: '加载聚合接口列表出错',
          closeBtn: 0,
          shadeClose: true,
          content: ex.responseText
        });
      }
    });
  }

  // 初始化应用
  $(function() {
    initApp();
  });
})();
