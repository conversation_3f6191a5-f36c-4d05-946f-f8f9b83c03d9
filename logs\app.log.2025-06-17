2025-06-17 09:48:16 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-17 09:48:17 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-17 09:48:17 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-17 09:48:17 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-17 09:48:17 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-17 09:48:17 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-17 09:48:17 | INFO  | base.py        :start                | Scheduler started
2025-06-17 09:48:17 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-17 09:48:17 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.083s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.076s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.077s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.083s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.080s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.076s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.009s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.011s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.007s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.009s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.010s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.012s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.008s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.010s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.008s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.010s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.007s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.010s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.007s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.005s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.009s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-17 09:48:20) - 0.009s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-17 09:48:20) - 0.014s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 304 (from=192.168.5.235,2025-06-17 09:48:20) - 0.002s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-17 09:48:20) - 0.011s
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-17 09:48:20 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-17 09:48:20) - 0.003s
2025-06-17 09:48:26 | INFO  | main.py        :log_requests         | [GET]/docs -- (from=192.168.5.235)
2025-06-17 09:48:26 | INFO  | main.py        :log_requests         | [GET]/docs? -- 200 (from=192.168.5.235,2025-06-17 09:48:26) - 0.001s
2025-06-17 09:48:26 | INFO  | main.py        :log_requests         | [GET]/openapi.json -- (from=192.168.5.235)
2025-06-17 09:48:26 | INFO  | main.py        :log_requests         | [GET]/openapi.json? -- 200 (from=192.168.5.235,2025-06-17 09:48:26) - 0.021s
2025-06-17 17:09:35 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-17 17:09:35 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-17 17:09:35 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-17 17:09:35 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-17 17:09:36 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-17 17:09:36 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-17 17:09:36 | INFO  | base.py        :start                | Scheduler started
2025-06-17 17:09:36 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-17 17:09:36 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-17 17:11:16 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:11:16 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:11:16) - 0.095s
2025-06-17 17:11:20 | INFO  | main.py        :log_requests         | [GET]/favicon.ico -- (from=192.168.5.235)
2025-06-17 17:11:20 | INFO  | main.py        :log_requests         | [GET]/favicon.ico? -- 404 (from=192.168.5.235,2025-06-17 17:11:20) - 0.002s
2025-06-17 17:12:34 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:12:34 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:12:34) - 0.003s
2025-06-17 17:12:53 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:12:53 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:12:53) - 0.002s
2025-06-17 17:13:38 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:13:38 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 304 (from=192.168.5.235,2025-06-17 17:13:38) - 0.003s
2025-06-17 17:15:12 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:15:12 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:15:12) - 0.001s
2025-06-17 17:15:33 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:15:33 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:15:33) - 0.002s
2025-06-17 17:15:34 | INFO  | main.py        :log_requests         | [GET]/favicon.ico -- (from=192.168.5.235)
2025-06-17 17:15:34 | INFO  | main.py        :log_requests         | [GET]/favicon.ico? -- 404 (from=192.168.5.235,2025-06-17 17:15:34) - 0.001s
2025-06-17 17:15:41 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:15:41 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:15:41) - 0.001s
2025-06-17 17:16:32 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:16:32 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:16:32) - 0.001s
2025-06-17 17:17:01 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:17:01 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:17:01) - 0.002s
2025-06-17 17:17:36 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:17:36 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:17:36) - 0.001s
2025-06-17 17:18:12 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:18:12 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:18:12) - 0.002s
2025-06-17 17:18:39 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:18:39 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:18:39) - 0.001s
2025-06-17 17:19:22 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:19:22 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 304 (from=192.168.5.235,2025-06-17 17:19:22) - 0.001s
2025-06-17 17:19:35 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:19:35 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:19:35) - 0.002s
2025-06-17 17:19:54 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:19:54 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:19:54) - 0.002s
2025-06-17 17:20:05 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:20:05 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:20:05) - 0.002s
2025-06-17 17:20:14 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html -- (from=192.168.5.235)
2025-06-17 17:20:14 | INFO  | main.py        :log_requests         | [GET]/static/monitor.html? -- 200 (from=192.168.5.235,2025-06-17 17:20:14) - 0.002s
