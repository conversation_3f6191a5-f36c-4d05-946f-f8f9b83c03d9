2025-06-12 15:31:35 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:31:35 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:31:35 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:31:35 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:31:35 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:31:35 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:31:35 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:31:35 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:31:52 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 15:31:52 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 15:31:52) - 0.035s
2025-06-12 15:31:52 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- (from=127.0.0.1)
2025-06-12 15:31:52 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=127.0.0.1,2025-06-12 15:31:52) - 0.014s
2025-06-12 15:32:19 | INFO  | main.py        :log_requests         | [GET]/? -- (from=127.0.0.1)
2025-06-12 15:32:19 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=127.0.0.1,2025-06-12 15:32:19) - 0.057s
2025-06-12 15:32:19 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 15:32:19 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 15:32:19) - 0.010s
2025-06-12 15:32:19 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- (from=127.0.0.1)
2025-06-12 15:32:34 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=127.0.0.1,2025-06-12 15:32:19) - 14.687s
2025-06-12 15:32:34 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- (from=127.0.0.1)
2025-06-12 15:32:34 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- 404 (from=127.0.0.1,2025-06-12 15:32:34) - 0.002s
2025-06-12 15:32:34 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- (from=127.0.0.1)
2025-06-12 15:32:34 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=127.0.0.1,2025-06-12 15:32:34) - 0.002s
2025-06-12 15:32:38 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:32:38 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:32:38) - 0.012s
2025-06-12 15:37:22 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:37:22 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:37:22 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:37:23 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:37:23 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:37:23 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:37:23 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:37:23 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:37:23 | INFO  | base.py        :shutdown             | Scheduler has been shut down
2025-06-12 15:37:23 | INFO  | main.py        :shutdown_event       | 定时任务已关闭
2025-06-12 15:37:35 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:37:35 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:37:35 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:37:35 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:37:35 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:37:35 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:37:35 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:37:35 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:40:49 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:40:50 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:40:50 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:40:50 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:40:50 | INFO  | main.py        :init_services        | 所有服务初始化完成
2025-06-12 15:40:50 | INFO  | main.py        :startup_event        | 服务初始化成功
2025-06-12 15:40:50 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:40:50 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:40:50 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:40:50 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:41:12 | INFO  | main.py        :log_requests         | [GET]/health? -- (from=127.0.0.1)
2025-06-12 15:41:12 | INFO  | main.py        :log_requests         | [GET]/health? -- 200 (from=127.0.0.1,2025-06-12 15:41:12) - 0.001s
2025-06-12 15:41:20 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 15:41:20 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 15:41:20) - 0.005s
2025-06-12 15:41:56 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:41:56 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:41:56 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:41:56 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:41:56 | INFO  | main.py        :init_services        | 所有服务初始化完成
2025-06-12 15:41:56 | INFO  | main.py        :startup_event        | 服务初始化成功
2025-06-12 15:41:56 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:41:56 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:41:56 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:41:56 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:42:00 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:42:00 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:42:00) - 0.021s
2025-06-12 15:42:05 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:42:05 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:42:05) - 0.015s
2025-06-12 15:42:07 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- (from=127.0.0.1)
2025-06-12 15:42:07 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- 404 (from=127.0.0.1,2025-06-12 15:42:07) - 0.001s
2025-06-12 15:42:07 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- (from=127.0.0.1)
2025-06-12 15:42:07 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=127.0.0.1,2025-06-12 15:42:07) - 0.001s
2025-06-12 15:42:11 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:42:11 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:42:11) - 0.016s
2025-06-12 15:42:30 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:42:31 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:42:31 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:42:31 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:42:31 | INFO  | main.py        :init_services        | 所有服务初始化完成
2025-06-12 15:42:31 | INFO  | main.py        :startup_event        | 服务初始化成功
2025-06-12 15:42:31 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:42:31 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:42:31 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:42:31 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:42:43 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:42:43 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:42:43) - 0.019s
2025-06-12 15:44:48 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:44:48 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:44:48 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:44:48 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-12 15:44:48 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:44:48 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:44:48 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:44:48 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:44:48 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:44:51 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:44:51 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:44:51) - 0.013s
2025-06-12 15:45:08 | INFO  | main.py        :log_requests         | [GET]/health? -- (from=127.0.0.1)
2025-06-12 15:45:08 | INFO  | main.py        :log_requests         | [GET]/health? -- 200 (from=127.0.0.1,2025-06-12 15:45:08) - 0.001s
2025-06-12 15:45:15 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 15:45:15 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 15:45:15) - 0.004s
2025-06-12 15:45:47 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:45:47 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:45:47 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:45:47 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-12 15:45:47 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:45:47 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:45:47 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:45:47 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:45:47 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:45:51 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:45:51 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:45:51) - 0.019s
2025-06-12 15:49:00 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:49:01 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:49:01 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:49:01 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-12 15:49:01 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:49:01 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:49:01 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:49:01 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:49:01 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:49:06 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:49:12 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:49:06) - 5.600s
2025-06-12 15:51:35 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- (from=127.0.0.1)
2025-06-12 15:51:35 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- (from=127.0.0.1)
2025-06-12 15:51:35 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- 404 (from=127.0.0.1,2025-06-12 15:51:35) - 0.002s
2025-06-12 15:51:35 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=127.0.0.1,2025-06-12 15:51:35) - 0.001s
2025-06-12 15:51:38 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:51:42 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:51:38) - 4.193s
2025-06-12 15:55:53 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:55:53 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:55:53 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:55:53 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-12 15:55:53 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:55:53 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:55:53 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:55:53 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:55:53 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:56:13 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 15:56:13 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 15:56:13) - 0.004s
2025-06-12 15:56:22 | INFO  | main.py        :log_requests         | [GET]/order/get?plat=1&pagesize=5 -- (from=127.0.0.1)
2025-06-12 15:56:22 | INFO  | main.py        :log_requests         | [GET]/order/get?plat=1&pagesize=5 -- 200 (from=127.0.0.1,2025-06-12 15:56:22) - 0.039s
2025-06-12 15:57:03 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 15:57:04 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 15:57:04 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 15:57:04 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-12 15:57:07 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 15:57:07 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 15:57:07 | INFO  | base.py        :start                | Scheduler started
2025-06-12 15:57:07 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 15:57:07 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 15:57:14 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:57:14 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:14) - 0.015s
2025-06-12 15:57:20 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:57:20 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:20) - 0.012s
2025-06-12 15:57:20 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:57:20 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:20) - 0.010s
2025-06-12 15:57:39 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- (from=127.0.0.1)
2025-06-12 15:57:39 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:39) - 0.014s
2025-06-12 15:57:40 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- (from=127.0.0.1)
2025-06-12 15:57:40 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- 200 (from=127.0.0.1,2025-06-12 15:57:40) - 0.015s
2025-06-12 15:57:41 | INFO  | main.py        :log_requests         | [POST]/common/get? -- (from=127.0.0.1)
2025-06-12 15:57:41 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:41) - 0.023s
2025-06-12 15:57:42 | INFO  | main.py        :log_requests         | [POST]/record/get? -- (from=127.0.0.1)
2025-06-12 15:57:42 | INFO  | main.py        :log_requests         | [POST]/record/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:42) - 0.054s
2025-06-12 15:57:44 | INFO  | main.py        :log_requests         | [POST]/record/get? -- (from=127.0.0.1)
2025-06-12 15:57:44 | INFO  | main.py        :log_requests         | [POST]/record/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:44) - 0.012s
2025-06-12 15:57:45 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 15:57:45 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 15:57:45) - 0.018s
2025-06-12 16:00:31 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 16:00:31 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 16:00:31 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 16:00:31 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-12 16:00:31 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 16:00:31 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 16:00:31 | INFO  | base.py        :start                | Scheduler started
2025-06-12 16:00:31 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 16:00:31 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 16:00:50 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 16:00:50 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 16:00:50) - 0.008s
2025-06-12 16:00:57 | INFO  | main.py        :log_requests         | [GET]/order/get?plat=1&pagesize=3 -- (from=127.0.0.1)
2025-06-12 16:00:57 | INFO  | main.py        :log_requests         | [GET]/order/get?plat=1&pagesize=3 -- 200 (from=127.0.0.1,2025-06-12 16:00:57) - 0.008s
2025-06-12 16:04:19 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-12 16:04:20 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-12 16:04:20 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-12 16:04:20 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-12 16:04:20 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-12 16:04:20 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-12 16:04:20 | INFO  | base.py        :start                | Scheduler started
2025-06-12 16:04:20 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-12 16:04:20 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-12 16:04:27 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:04:27 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:04:27) - 0.013s
2025-06-12 16:05:35 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:05:35 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:05:35) - 0.009s
2025-06-12 16:06:10 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- (from=127.0.0.1)
2025-06-12 16:06:10 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- 404 (from=127.0.0.1,2025-06-12 16:06:10) - 0.002s
2025-06-12 16:06:10 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- (from=127.0.0.1)
2025-06-12 16:06:10 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=127.0.0.1,2025-06-12 16:06:10) - 0.001s
2025-06-12 16:07:18 | INFO  | main.py        :log_requests         | [GET]/? -- (from=127.0.0.1)
2025-06-12 16:07:18 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=127.0.0.1,2025-06-12 16:07:18) - 0.056s
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- (from=127.0.0.1)
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- 404 (from=127.0.0.1,2025-06-12 16:07:19) - 0.001s
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- (from=127.0.0.1)
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=127.0.0.1,2025-06-12 16:07:19) - 0.001s
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 16:07:19) - 0.014s
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- (from=127.0.0.1)
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=127.0.0.1,2025-06-12 16:07:19) - 0.024s
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:07:19) - 0.014s
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:07:19 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:07:19) - 0.003s
2025-06-12 16:07:45 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:07:45 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:07:45) - 0.011s
2025-06-12 16:07:45 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:07:45 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:07:45) - 0.003s
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/? -- (from=127.0.0.1)
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=127.0.0.1,2025-06-12 16:07:50) - 0.002s
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- (from=127.0.0.1)
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- 404 (from=127.0.0.1,2025-06-12 16:07:50) - 0.001s
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- (from=127.0.0.1)
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=127.0.0.1,2025-06-12 16:07:50) - 0.001s
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 16:07:50) - 0.012s
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- (from=127.0.0.1)
2025-06-12 16:07:50 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=127.0.0.1,2025-06-12 16:07:50) - 0.009s
2025-06-12 16:08:01 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:08:01 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:08:01) - 0.011s
2025-06-12 16:08:01 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:01 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:01) - 0.003s
2025-06-12 16:08:05 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:05 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:05) - 0.004s
2025-06-12 16:08:05 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:05 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:05) - 0.003s
2025-06-12 16:08:06 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:06 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:06) - 0.003s
2025-06-12 16:08:06 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:06 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:06) - 0.003s
2025-06-12 16:08:06 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:06 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:06) - 0.002s
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:07) - 0.002s
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:07) - 0.003s
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:07) - 0.003s
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:07 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:07) - 0.002s
2025-06-12 16:08:12 | INFO  | main.py        :log_requests         | [GET]/static/platAdd.html? -- (from=127.0.0.1)
2025-06-12 16:08:12 | INFO  | main.py        :log_requests         | [GET]/static/platAdd.html? -- 200 (from=127.0.0.1,2025-06-12 16:08:12) - 0.003s
2025-06-12 16:08:14 | INFO  | main.py        :log_requests         | [GET]/static/orderLibraryAdd.html? -- (from=127.0.0.1)
2025-06-12 16:08:14 | INFO  | main.py        :log_requests         | [GET]/static/orderLibraryAdd.html? -- 200 (from=127.0.0.1,2025-06-12 16:08:14) - 0.002s
2025-06-12 16:08:14 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 16:08:14 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 16:08:14) - 0.008s
2025-06-12 16:08:20 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 16:08:20 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 16:08:20) - 0.010s
2025-06-12 16:08:30 | INFO  | main.py        :log_requests         | [POST]/order/add? -- (from=127.0.0.1)
2025-06-12 16:08:30 | INFO  | main.py        :log_requests         | [POST]/order/add? -- 200 (from=127.0.0.1,2025-06-12 16:08:30) - 0.014s
2025-06-12 16:08:30 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:08:30 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:08:30) - 0.011s
2025-06-12 16:08:30 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:30 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:30) - 0.003s
2025-06-12 16:08:41 | INFO  | main.py        :log_requests         | [POST]/order/update? -- (from=127.0.0.1)
2025-06-12 16:08:41 | INFO  | main.py        :log_requests         | [POST]/order/update? -- 200 (from=127.0.0.1,2025-06-12 16:08:41) - 0.019s
2025-06-12 16:08:41 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:08:41 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:08:41) - 0.011s
2025-06-12 16:08:41 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:41 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:41) - 0.003s
2025-06-12 16:08:42 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:42 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:42) - 0.003s
2025-06-12 16:08:42 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:42 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:42) - 0.002s
2025-06-12 16:08:45 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:45 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:45) - 0.003s
2025-06-12 16:08:45 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:45 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:45) - 0.002s
2025-06-12 16:08:48 | INFO  | main.py        :log_requests         | [POST]/order/del? -- (from=127.0.0.1)
2025-06-12 16:08:48 | INFO  | main.py        :log_requests         | [POST]/order/del? -- 200 (from=127.0.0.1,2025-06-12 16:08:48) - 0.013s
2025-06-12 16:08:48 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:08:48 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:08:48) - 0.011s
2025-06-12 16:08:48 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:48 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:48) - 0.004s
2025-06-12 16:08:50 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:50 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:50) - 0.003s
2025-06-12 16:08:50 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:50 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:50) - 0.003s
2025-06-12 16:08:50 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:50 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:50) - 0.003s
2025-06-12 16:08:51 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- (from=127.0.0.1)
2025-06-12 16:08:51 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=127.0.0.1,2025-06-12 16:08:51) - 0.014s
2025-06-12 16:08:51 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:51 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:51) - 0.003s
2025-06-12 16:08:51 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:51 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:51) - 0.003s
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:52) - 0.003s
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:52) - 0.003s
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:52) - 0.002s
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:52 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:52) - 0.003s
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:53) - 0.003s
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:53) - 0.003s
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:53) - 0.002s
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:08:53 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:53) - 0.002s
2025-06-12 16:08:55 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:08:55 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:08:55) - 0.018s
2025-06-12 16:08:55 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:08:55 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:08:55) - 0.004s
2025-06-12 16:08:56 | INFO  | main.py        :log_requests         | [GET]/static/orderPush.html? -- (from=127.0.0.1)
2025-06-12 16:08:56 | INFO  | main.py        :log_requests         | [GET]/static/orderPush.html? -- 200 (from=127.0.0.1,2025-06-12 16:08:56) - 0.003s
2025-06-12 16:08:57 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:09:16 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:08:57) - 19.800s
2025-06-12 16:09:30 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:09:30 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:09:30) - 0.006s
2025-06-12 16:09:36 | INFO  | main.py        :log_requests         | [GET]/? -- (from=127.0.0.1)
2025-06-12 16:09:36 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=127.0.0.1,2025-06-12 16:09:36) - 0.002s
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 16:09:37) - 0.019s
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- (from=127.0.0.1)
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=127.0.0.1,2025-06-12 16:09:37) - 0.011s
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:09:37) - 0.014s
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:09:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:09:37) - 0.003s
2025-06-12 16:09:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:09:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:09:38) - 0.004s
2025-06-12 16:09:38 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:09:38 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:09:38) - 0.006s
2025-06-12 16:09:42 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:09:42 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:09:42) - 0.442s
2025-06-12 16:09:54 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- (from=127.0.0.1)
2025-06-12 16:09:54 | INFO  | main.py        :log_requests         | [GET]/.well-known/appspecific/com.chrome.devtools.json? -- 404 (from=127.0.0.1,2025-06-12 16:09:54) - 0.001s
2025-06-12 16:09:54 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- (from=127.0.0.1)
2025-06-12 16:09:54 | INFO  | main.py        :log_requests         | [GET]/sm/64d6754651b08011e56029ac6df83bb47c5a570dab69e24e289f71fd97eb927d.map? -- 404 (from=127.0.0.1,2025-06-12 16:09:54) - 0.001s
2025-06-12 16:09:59 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:09:59 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:09:59) - 0.006s
2025-06-12 16:11:20 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- (from=127.0.0.1)
2025-06-12 16:11:20 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=127.0.0.1,2025-06-12 16:11:20) - 0.005s
2025-06-12 16:11:20 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:11:20 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:11:20) - 0.003s
2025-06-12 16:11:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- (from=127.0.0.1)
2025-06-12 16:11:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- 200 (from=127.0.0.1,2025-06-12 16:11:22) - 0.007s
2025-06-12 16:11:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById? -- (from=127.0.0.1)
2025-06-12 16:11:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById? -- 200 (from=127.0.0.1,2025-06-12 16:11:22) - 0.030s
2025-06-12 16:11:23 | INFO  | main.py        :log_requests         | [POST]/common/get? -- (from=127.0.0.1)
2025-06-12 16:11:23 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=127.0.0.1,2025-06-12 16:11:23) - 0.006s
2025-06-12 16:11:23 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- (from=127.0.0.1)
2025-06-12 16:11:23 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=127.0.0.1,2025-06-12 16:11:23) - 0.003s
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- (from=127.0.0.1)
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=127.0.0.1,2025-06-12 16:11:24) - 0.004s
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- (from=127.0.0.1)
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=127.0.0.1,2025-06-12 16:11:24) - 0.003s
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:11:24) - 0.015s
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:11:24 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:11:24) - 0.003s
2025-06-12 16:11:45 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:11:58 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:11:45) - 13.031s
2025-06-12 16:12:27 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:12:27 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:12:27) - 0.010s
2025-06-12 16:12:29 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:12 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:12:29) - 42.373s
2025-06-12 16:13:19 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:25 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:19) - 6.556s
2025-06-12 16:13:27 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:30 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:27) - 2.817s
2025-06-12 16:13:36 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:36 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:36) - 0.007s
2025-06-12 16:13:39 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:13:39 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:13:39) - 0.002s
2025-06-12 16:13:41 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:41 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:41) - 0.005s
2025-06-12 16:13:43 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:13:43 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:13:43) - 0.003s
2025-06-12 16:13:44 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:44 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:44) - 0.008s
2025-06-12 16:13:46 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:13:46 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:13:46) - 0.004s
2025-06-12 16:13:47 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:47 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:47) - 0.008s
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [GET]/? -- (from=127.0.0.1)
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=127.0.0.1,2025-06-12 16:13:51) - 0.002s
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- (from=127.0.0.1)
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=127.0.0.1,2025-06-12 16:13:51) - 0.010s
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- (from=127.0.0.1)
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=127.0.0.1,2025-06-12 16:13:51) - 0.010s
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [POST]/order/get? -- (from=127.0.0.1)
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=127.0.0.1,2025-06-12 16:13:51) - 0.015s
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:13:51 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:13:51) - 0.003s
2025-06-12 16:13:52 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:52 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:52) - 0.005s
2025-06-12 16:13:55 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:13:55 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:13:55) - 0.006s
2025-06-12 16:14:00 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:00 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:00) - 0.005s
2025-06-12 16:14:03 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:03 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:03) - 0.006s
2025-06-12 16:14:04 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:04 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:04) - 0.063s
2025-06-12 16:14:05 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:05 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:05) - 0.005s
2025-06-12 16:14:08 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:08 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:08) - 0.306s
2025-06-12 16:14:10 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:10 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:10) - 0.006s
2025-06-12 16:14:11 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:11 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:11) - 0.003s
2025-06-12 16:14:12 | INFO  | main.py        :log_requests         | [GET]/static/openApiOrderPush.html? -- (from=127.0.0.1)
2025-06-12 16:14:12 | INFO  | main.py        :log_requests         | [GET]/static/openApiOrderPush.html? -- 200 (from=127.0.0.1,2025-06-12 16:14:12) - 0.003s
2025-06-12 16:14:15 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:15 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:15) - 0.006s
2025-06-12 16:14:18 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:18 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:18) - 0.005s
2025-06-12 16:14:21 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:21 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:21) - 0.007s
2025-06-12 16:14:22 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:22 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:22) - 0.013s
2025-06-12 16:14:23 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:23 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:23) - 0.007s
2025-06-12 16:14:24 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:24 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:24) - 0.007s
2025-06-12 16:14:31 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:31 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:31) - 0.003s
2025-06-12 16:14:33 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:33 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:33) - 0.003s
2025-06-12 16:14:33 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:33 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:33) - 0.003s
2025-06-12 16:14:34 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:34 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:34) - 0.003s
2025-06-12 16:14:35 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:35 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:35) - 0.002s
2025-06-12 16:14:35 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:35 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:35) - 0.003s
2025-06-12 16:14:35 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:35 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:35) - 0.003s
2025-06-12 16:14:36 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:36 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:36) - 0.003s
2025-06-12 16:14:36 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:36 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:36) - 0.003s
2025-06-12 16:14:36 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:36 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:36) - 0.003s
2025-06-12 16:14:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:37) - 0.003s
2025-06-12 16:14:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:37) - 0.002s
2025-06-12 16:14:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:37 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:37) - 0.003s
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:38) - 0.003s
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:38) - 0.002s
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:38) - 0.002s
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:38 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:38) - 0.003s
2025-06-12 16:14:39 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:39 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:39) - 0.003s
2025-06-12 16:14:39 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:39 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:39) - 0.003s
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:40) - 0.002s
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:40) - 0.002s
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:40) - 0.002s
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:40) - 0.002s
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- (from=127.0.0.1)
2025-06-12 16:14:40 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=127.0.0.1,2025-06-12 16:14:40) - 0.003s
2025-06-12 16:14:41 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:14:41 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:14:41) - 0.006s
2025-06-12 16:14:44 | INFO  | main.py        :log_requests         | [POST]/order/push? -- (from=127.0.0.1)
2025-06-12 16:14:44 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:14:44 正在以会员420001 账号747身份登录
2025-06-12 16:14:47 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:14:47 登录成功
2025-06-12 16:15:02 | INFO  | service.py     :pushByExample        | 推送订单号：1933075481101385728 
 
2025-06-12 16:15:02 | INFO  | main.py        :log_requests         | [POST]/order/push? -- 200 (from=127.0.0.1,2025-06-12 16:14:44) - 18.224s
2025-06-12 16:15:11 | INFO  | main.py        :log_requests         | [POST]/order/push? -- (from=127.0.0.1)
2025-06-12 16:15:11 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:15:11 正在以会员420001 账号747身份登录
2025-06-12 16:15:14 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:15:14 登录成功
2025-06-12 16:15:21 | INFO  | service.py     :pushByExample        | 推送订单号：1933075596264390656 
 
2025-06-12 16:15:21 | INFO  | main.py        :log_requests         | [POST]/order/push? -- 200 (from=127.0.0.1,2025-06-12 16:15:11) - 10.131s
2025-06-12 16:15:26 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- (from=127.0.0.1)
2025-06-12 16:15:26 | INFO  | main.py        :log_requests         | [POST]/shop/getShopByPlat? -- 200 (from=127.0.0.1,2025-06-12 16:15:26) - 0.006s
2025-06-12 16:15:29 | INFO  | main.py        :log_requests         | [POST]/order/push? -- (from=127.0.0.1)
2025-06-12 16:15:29 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:15:29 正在以会员440001 账号llw身份登录
2025-06-12 16:15:31 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:15:31 登录成功
2025-06-12 16:15:39 | INFO  | service.py     :pushByExample        | 推送订单号：1933075670990110720 
 
2025-06-12 16:15:39 | INFO  | main.py        :log_requests         | [POST]/order/push? -- 200 (from=127.0.0.1,2025-06-12 16:15:29) - 10.067s
2025-06-12 16:15:43 | INFO  | main.py        :log_requests         | [POST]/order/push? -- (from=127.0.0.1)
2025-06-12 16:15:43 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:15:43 正在以会员440001 账号llw身份登录
2025-06-12 16:16:17 | INFO  | jackyunclient.py:__print_log          | 2025-06-12 16:16:17 登录成功
2025-06-12 16:16:17 | INFO  | service.py     :pushByExample        | 推送订单号：1933075728292691968 
 
2025-06-12 16:16:17 | INFO  | main.py        :log_requests         | [POST]/order/push? -- 200 (from=127.0.0.1,2025-06-12 16:15:43) - 34.184s
