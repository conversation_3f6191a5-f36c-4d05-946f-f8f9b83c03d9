#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多态方法配置仓储使用示例
"""

from core.repository import polyMethodConfigRepository

def example_usage():
    """展示如何使用 polyMethodConfigRepository"""
    
    # 创建仓储实例
    repo = polyMethodConfigRepository()
    
    # 1. 插入新配置
    print("1. 插入新配置:")
    try:
        result = repo.insert(
            method='getOrderList',
            req_feild='orders',
            return_feild='orderlist',
            feild_map='{"platorderno":"order_no","orderstatus":"status"}'
        )
        print(f"   插入结果: {result}")
    except Exception as e:
        print(f"   插入失败: {e}")
    
    # 2. 根据方法名查询配置
    print("\n2. 根据方法名查询配置:")
    try:
        configs = repo.getByMethod('getOrderList')
        print(f"   查询到 {len(configs)} 条配置")
        for config in configs:
            print(f"   - ID: {config.id}, 请求字段: {config.req_feild}, 返回字段: {config.return_feild}")
    except Exception as e:
        print(f"   查询失败: {e}")
    
    # 3. 根据方法名和字段查询特定配置
    print("\n3. 根据方法名和字段查询特定配置:")
    try:
        configs = repo.getByMethodAndFields('getOrderList', 'orders', 'orderlist')
        print(f"   查询到 {len(configs)} 条匹配配置")
    except Exception as e:
        print(f"   查询失败: {e}")
    
    # 4. 分页查询所有配置
    print("\n4. 分页查询所有配置:")
    try:
        reqVars = {
            'page': 1,
            'limit': 10,
            'method': 'getOrderList'  # 可选的过滤条件
        }
        configs, total_count = repo.query(reqVars)
        print(f"   查询到 {len(configs)} 条配置，总计 {total_count} 条")
    except Exception as e:
        print(f"   查询失败: {e}")
    
    # 5. 查询单个配置
    print("\n5. 查询单个配置:")
    try:
        config = repo.getOne(1)  # 假设ID为1的配置存在
        if config:
            print(f"   找到配置: {config[0].method if config else 'None'}")
        else:
            print("   未找到指定配置")
    except Exception as e:
        print(f"   查询失败: {e}")
    
    # 6. 更新配置
    print("\n6. 更新配置:")
    try:
        result = repo.update(
            id=1,  # 假设要更新ID为1的配置
            method='getOrderList',
            req_feild='orders',
            return_feild='orderlist',
            feild_map='{"platorderno":"order_no","orderstatus":"status","createtime":"create_time"}'
        )
        print(f"   更新结果: {result}")
    except Exception as e:
        print(f"   更新失败: {e}")
    
    # 7. 删除配置
    print("\n7. 删除配置:")
    try:
        result = repo.delete(1)  # 假设要删除ID为1的配置
        print(f"   删除结果: {result}")
    except Exception as e:
        print(f"   删除失败: {e}")

if __name__ == '__main__':
    print("多态方法配置仓储使用示例")
    print("=" * 50)
    example_usage()
