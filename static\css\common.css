body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Aria<PERSON>, sans-serif;
}

.layui-table,
.layui-table-view {
  margin: 5px 0 0 0;
}

.center-body table th {
  font-weight: bold !important;
  text-align: center !important;
}

.center-body {
  min-height: 100%;
  margin: 0px 5px 0px 75px;
}

.item-hide {
  display: none;
}

.layui-table-page div {
  float: right;
}

.layui-nav {
  width: 70px !important;
}

.layui-nav-item>a {
  cursor: pointer;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: var(--el-button-background-color);
}

.layui-btn+.layui-btn {
    margin-left: 1px;
}
.layui-form-label {
  padding-left: 0px;
  padding-right: 5px;
}

.layui-table-cell {
  height: 39px;
}

.form-resubmit {
  float: right;
}

.form-div .layui-form {
  margin: 5px !important;
  float: right;
}

.skin-switch {
  position: absolute;
  z-index: 999;
  bottom: 25px;
  left: 10px;
}

.push-notify-form-item {
  width: 300px !important;
}

/* 工具栏样式 */
.toolbar-container {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.toolbar-left {
  display: flex;
  flex: 1;
}

.toolbar-right {
  display: flex;
  justify-content: flex-end;
}

/* 左右分栏布局 */
.split-container {
  display: flex;
  width: 100%;
}

.left-panel {
  width: 60%;
}

.right-panel {
  width: 40%;
  margin: 5px;
}

/* 日志界面特殊样式 */
.record-container {
  width: 100%;
}

/* 说明界面样式 */
#guide-div {
  overflow: auto;
  height: calc(100vh - 40px);
}

#guide-div > div {
  display: flex;
  justify-content: center;
  width: 100%;
}

.markdown-body {
  box-sizing: border-box;
  min-width: 200px;
  max-width: 980px;
  margin: 0 auto;
  padding: 45px;
  background-color: var(--markdown-body-bg-color, #fff);
}

@media (max-width: 767px) {
  .markdown-body {
    padding: 15px;
  }
}

/* 深色模式下的 Markdown 样式 */
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body {
  --markdown-body-bg-color: #0d1117;
  color: #c9d1d9;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body a {
  color: #58a6ff;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h1,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h2,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h3,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h4,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h5,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body h6 {
  color: #e6edf3;
  border-bottom-color: #21262d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body hr {
  background-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body blockquote {
  color: #8b949e;
  border-left-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body code {
  background-color: rgba(110, 118, 129, 0.4);
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body pre {
  background-color: #161b22;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table tr {
  background-color: #0d1117;
  border-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table tr:nth-child(2n) {
  background-color: #161b22;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table th,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .markdown-body table td {
  border-color: #30363d;
}

/* 字段映射CRUD界面样式 */
#mapping-table {
  border-collapse: collapse;
  width: 100%;
}

#mapping-table th,
#mapping-table td {
  border: 1px solid #e6e6e6;
  padding: 8px;
  vertical-align: middle;
}

#mapping-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

#mapping-table .layui-input {
  border: 1px solid #e6e6e6;
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
  width: 100%;
  box-sizing: border-box;
}

#mapping-table .layui-input:focus {
  border-color: #1E9FFF;
}

/* 操作按钮样式 */
#mapping-table .add-mapping-btn {
  margin-right: 5px;
}

#mapping-table .layui-btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  border-radius: 2px;
}

/* 深色模式下的字段映射样式 */
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table th,
[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table td {
  border-color: #30363d;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table th {
  background-color: #21262d;
  color: #e6edf3;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table .layui-input {
  background-color: #0d1117;
  border-color: #30363d;
  color: #c9d1d9;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body #mapping-table .layui-input:focus {
  border-color: #1E9FFF;
}

[href="./static/js/layui/css/layui-theme-dark.css"] ~ body .mapping-dialog-toolbar {
  border-bottom-color: #30363d;
}
