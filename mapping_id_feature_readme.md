# common/getById 接口 mappingId 字段功能

## 功能概述

为 `common/getById` 接口的返回结果新增了 `mappingId` 字段，用于关联多态方法配置表中的相关记录。

## 实现逻辑

### 赋值规则

当返回结果中的 `biz_feild` 字段值不为空时：
1. 使用 `method` 字段值和 `biz_feild` 字段值作为查询条件
2. 从 `glo_poly_simulator_poly_method_config` 表中查询满足以下条件的记录：
   - `method` = 返回结果的 `method` 字段值
   - `return_feild` = 返回结果的 `biz_feild` 字段值
3. 取查询到的第一条记录的 `id` 作为 `mappingId` 的值
4. 如果查询不到匹配记录，`mappingId` 为空字符串

### 代码实现

#### 1. 新增查询方法

在 `polyMethodConfigRepository` 类中新增了 `getByMethodAndReturnField` 方法：

```python
def getByMethodAndReturnField(self, method_name, return_feild):
    """根据方法名和返回字段查询配置（用于mappingId查询）"""
    lst = []
    try:
        if self.db is None:
            return lst
        sql = "select * from glo_poly_simulator_poly_method_config where method = '{}' and return_feild = '{}' limit 1".format(
            method_name, return_feild)
        lst = mysqlHelper.selectList(sql, self.db)
    except Exception as e:
        logging.error('根据方法名和返回字段查询配置出错：' + str(e))
    return lst
```

#### 2. 修改 getById 方法

在 `commonExampleService.getById` 方法中添加了 `mappingId` 字段的处理逻辑：

```python
def getById(self, postvars):
    entityId = postvars['id']
    retList = self.repo.getOne(entityId) if entityId else []
    
    # 为每个返回的记录添加 mappingId 字段
    if retList:
        from core.repository import polyMethodConfigRepository
        poly_config_repo = polyMethodConfigRepository()
        
        for item in retList:
            # 初始化 mappingId 为空
            item.mappingId = ''
            
            # 当 biz_feild 字段值不为空时，查询 mappingId
            if hasattr(item, 'biz_feild') and item.biz_feild and hasattr(item, 'method') and item.method:
                try:
                    # 根据 method 和 biz_feild（作为return_feild）查询配置
                    configs = poly_config_repo.getByMethodAndReturnField(item.method, item.biz_feild)
                    if configs and len(configs) > 0:
                        # 取第一个匹配的配置的 id
                        item.mappingId = str(configs[0].id)
                except Exception as e:
                    logging.error(f'查询mappingId出错：{str(e)}')
                    item.mappingId = ''
    
    return jsonHelper.toJson({'code': 0, 'msg': '', 'data': retList})
```

## 使用示例

### 请求示例

```http
POST /common/getById
Content-Type: application/json

{
    "id": "1"
}
```

### 响应示例

```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "id": 1,
            "plat": 1,
            "method": "getOrderList",
            "biz_feild": "orderlist",
            "content": "{...}",
            "tag": "test",
            "author_ip": "***********",
            "gmt_create": "2025-06-18 10:00:00",
            "gmt_modify": "2025-06-18 10:00:00",
            "mappingId": "2"
        }
    ]
}
```

## 注意事项

1. **性能考虑**: 每个返回记录都会执行一次数据库查询来获取 `mappingId`，在大量数据时可能影响性能
2. **异常处理**: 查询 `mappingId` 过程中的异常会被捕获并记录日志，不会影响主要数据的返回
3. **数据类型**: `mappingId` 始终返回字符串类型，即使原始 ID 是数字
4. **空值处理**: 当 `biz_feild` 为空或查询不到匹配记录时，`mappingId` 为空字符串

## 相关表结构

### glo_poly_simulator_example 表
- `method`: 方法名
- `biz_feild`: 业务字段名

### glo_poly_simulator_poly_method_config 表
- `id`: 配置ID（作为 mappingId 返回）
- `method`: 方法名
- `return_feild`: 返回字段名（与 biz_feild 匹配）

## 测试验证

功能已通过测试验证，能够正确：
1. 查询匹配的配置记录
2. 返回正确的 mappingId 值
3. 处理异常情况
4. 保持原有功能不受影响
