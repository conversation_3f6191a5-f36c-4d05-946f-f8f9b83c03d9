import logging
import threading

from core.base import *

lock = threading.Lock()

# 执行结果动态类
class sqlResult:
    def __init__(self,_obj):
        self.__dict__.update(_obj)

# mysql帮助类
class mysqlHelper:
    
    # 查询结果行转obj
    @staticmethod
    def to_obj(obj: object, **data):
        obj.__dict__.update(data)
        return obj

    @staticmethod
    def selectList(sql, conn):
        """
        指定sql查询数据
        :param sql: 条件查询语句
        :param conn: 数据库连接
        """
        ret = []
        try:
            lock.acquire()
            cursor = conn.cursor()
            # start_time = time.perf_counter()
            # 执行sql
            cursor.execute(sql)
            # logging.debug("[查询]%s ; 耗时 %s ms\r" % (sql, (time.perf_counter() - start_time) * 1000))

            # 列名
            columns = [col[0] for col in cursor.description]
            # 数据
            raw = cursor.fetchall()

            # 查询结果转换：
            ret = [
                sqlResult(dict(zip(columns, row)))
                for row in raw
            ]

            cursor.close()
        except Exception as e:
            logging.error("数据库查询失败: " + str(e))
        finally:
            lock.release()

        return ret
    
    @staticmethod
    def query(feilds, tableName, conditions, limitAndOrder, isQueryCount, conn):
        """
        按条件查询数据和总量
        :param feilds: 需要查询的字段
        :param tableName: 表名
        :param conditions: 查询条件
        :param limitAndOrder: 分页和排序
        :param isQueryCount: 是否需要查询总量
        :param conn: 数据库连接
        """
        ret = []
        count = 0
        try:

            sql = 'select {} from {} '.format(feilds, tableName)
            if len(conditions) > 0:
                sql += 'where {} '.format(conditions)
            
            cnt_sql = sql.replace(feilds, 'count(1)') if isQueryCount else ''

            if len(limitAndOrder) > 0:
                sql += limitAndOrder
            else:
                sql += 'limit 5'

            lock.acquire()
            cursor = conn.cursor()
            # 执行sql查询数据
            cursor.execute(sql)
            # 列名
            columns = [col[0] for col in cursor.description]
            # 数据
            raw = cursor.fetchall()
            # 查询结果转换：
            ret = [
                sqlResult(dict(zip(columns, row)))
                for row in raw
            ]

            # 查询总量
            count = len(ret)
            if isQueryCount:
                cursor.execute(cnt_sql)
                count = cursor.fetchone()[0]

            cursor.close()
        except Exception as e:
            logging.error("数据库查询失败: " + str(e))
        finally:
            lock.release()
            return ret, count

    @staticmethod
    def doExecute(sql, conn):
        try:
            lock.acquire()
            cursor = conn.cursor()
            # 执行SQL，并返回守影响行数
            ret = cursor.execute(sql)
            # 提交，不然无法保存新建或者修改的数据
            conn.commit()
            # 关闭游标
            cursor.close()
            return 'success' if ret > 0 else '无数据变更'
        except Exception as e:
            logging.error("sql执行错误: {}\r\n".format(str(e)))
            ret = "sql: {}\r\n".format(sql)
            logging.error(ret)
            return ret
        finally:
            lock.release()        
