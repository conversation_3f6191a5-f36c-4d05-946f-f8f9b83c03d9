2025-06-14 11:05:35 | INFO  | server.py      :pre_process          | [GET]/? -- (from=192.168.5.235)
2025-06-14 11:05:35 | INFO  | server.py      :after_request        | [GET]/? -- 404 NOT FOUND (from=192.168.5.235,2025-06-14 11:11:35)
2025-06-14 11:05:35 | INFO  | server.py      :pre_process          | [GET]/favicon.ico? -- (from=192.168.5.235)
2025-06-14 11:05:35 | INFO  | server.py      :after_request        | [GET]/favicon.ico? -- 404 NOT FOUND (from=192.168.5.235,2025-06-14 11:11:35)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/jquery.min.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/css/layui.css? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/jquery.min.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/css/layui-theme-dark.css? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/layui/css/layui.css? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/css/common.css? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/layui/css/layui-theme-dark.css? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/css/common.css? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/md/github-markdown.min.css? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/layui.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/md/github-markdown.min.css? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/layui/layui.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/bignumber.min.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/jsonlint.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/bignumber.min.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/jsonlint.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/jquery.json.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/md/marked.min.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/jquery.json.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/templates.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/md/marked.min.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/templates.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/toolbar.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/sidebar.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/toolbar.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/sidebar.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/layout.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/order.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/layout.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/modules/order.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/goods.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/polymsg.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/modules/goods.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/modules/polymsg.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/common-module.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/record.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/modules/common-module.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/modules/guide.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/modules/record.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/modules/guide.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/app.js? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/app.js? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/plat/getall? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/plat/getall? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/common/getapi? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/common/getapi? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [POST]/order/get? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [POST]/order/get? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:06:01 | INFO  | server.py      :pre_process          | [POST]/order/getById? -- (from=192.168.5.235)
2025-06-14 11:06:01 | INFO  | server.py      :after_request        | [POST]/order/getById? -- 200 OK (from=192.168.5.235,2025-06-14 11:11:01)
2025-06-14 11:07:13 | INFO  | main.py        :<module>             | 日志系统初始化完成
2025-06-14 11:07:14 | INFO  | base.py        :add_job              | Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-14 11:07:14 | INFO  | main.py        :<module>             | 定时任务模块加载成功
2025-06-14 11:07:14 | INFO  | main.py        :<module>             | 所有服务初始化完成
2025-06-14 11:07:14 | INFO  | main.py        :startup_event        | FastAPI应用启动中...
2025-06-14 11:07:14 | INFO  | base.py        :_real_add_job        | Added job "清理过期记录" to job store "default"
2025-06-14 11:07:14 | INFO  | base.py        :start                | Scheduler started
2025-06-14 11:07:14 | INFO  | main.py        :startup_event        | 定时任务已启动
2025-06-14 11:07:14 | INFO  | main.py        :startup_event        | FastAPI应用启动完成
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/ -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/? -- 200 (from=192.168.5.235,2025-06-14 11:07:18) - 0.057s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.min.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.015s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/layui.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.015s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/bignumber.min.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.015s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/jsonlint.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.016s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/jquery.json.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.014s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui.css? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.017s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/css/layui-theme-dark.css? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.012s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/css/common.css? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.010s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/md/github-markdown.min.css? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.009s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/md/marked.min.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.010s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/toolbar.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.010s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/templates.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.012s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layout.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.008s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/sidebar.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.011s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/goods.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.008s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/order.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.011s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/polymsg.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.008s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/common-module.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.009s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/app.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.003s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/guide.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.005s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/modules/record.js? -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.006s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/plat/getall -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/plat/getall? -- 200 (from=192.168.5.235,2025-06-14 11:07:18) - 0.009s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/common/getapi -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/common/getapi? -- 200 (from=192.168.5.235,2025-06-14 11:07:18) - 0.014s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/js/layui/font/iconfont.woff2?v=282 -- 304 (from=192.168.5.235,2025-06-14 11:07:18) - 0.002s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [GET]/static/favicon.ico? -- 200 (from=192.168.5.235,2025-06-14 11:07:18) - 0.002s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:18) - 0.014s
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-14 11:07:18 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:18) - 0.003s
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/goods/get -- (from=192.168.5.235)
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:20) - 0.027s
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/goods/getById -- (from=192.168.5.235)
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:20) - 0.003s
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType -- (from=192.168.5.235)
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- 200 (from=192.168.5.235,2025-06-14 11:07:20) - 0.007s
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById -- (from=192.168.5.235)
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:20) - 0.014s
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:20) - 0.016s
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-14 11:07:20 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:20) - 0.004s
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/goods/get -- (from=192.168.5.235)
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:21) - 0.008s
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/goods/getById -- (from=192.168.5.235)
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:21) - 0.004s
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType -- (from=192.168.5.235)
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- 200 (from=192.168.5.235,2025-06-14 11:07:21) - 0.010s
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById -- (from=192.168.5.235)
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:21) - 0.018s
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:21) - 0.027s
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-14 11:07:21 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:21) - 0.023s
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/goods/get -- (from=192.168.5.235)
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/goods/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:22) - 0.009s
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/goods/getById -- (from=192.168.5.235)
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/goods/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:22) - 0.003s
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType -- (from=192.168.5.235)
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getApiByType? -- 200 (from=192.168.5.235,2025-06-14 11:07:22) - 0.009s
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById -- (from=192.168.5.235)
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/polymsg/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:22) - 0.008s
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:22) - 0.008s
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-14 11:07:22 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:22) - 0.003s
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:23) - 0.008s
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:23) - 0.003s
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/get -- (from=192.168.5.235)
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:23) - 0.009s
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/getById -- (from=192.168.5.235)
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/common/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:23) - 0.004s
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/record/get -- (from=192.168.5.235)
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [POST]/record/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:23) - 0.014s
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [GET]/static/doc/guide.md -- (from=192.168.5.235)
2025-06-14 11:07:23 | INFO  | main.py        :log_requests         | [GET]/static/doc/guide.md? -- 200 (from=192.168.5.235,2025-06-14 11:07:23) - 0.003s
2025-06-14 11:07:31 | INFO  | main.py        :log_requests         | [POST]/order/get -- (from=192.168.5.235)
2025-06-14 11:07:31 | INFO  | main.py        :log_requests         | [POST]/order/get? -- 200 (from=192.168.5.235,2025-06-14 11:07:31) - 0.020s
2025-06-14 11:07:31 | INFO  | main.py        :log_requests         | [POST]/order/getById -- (from=192.168.5.235)
2025-06-14 11:07:31 | INFO  | main.py        :log_requests         | [POST]/order/getById? -- 200 (from=192.168.5.235,2025-06-14 11:07:31) - 0.004s
