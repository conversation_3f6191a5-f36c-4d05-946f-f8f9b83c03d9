# 多态方法配置仓储 (polyMethodConfigRepository)

## 概述

`polyMethodConfigRepository` 是为 `glo_poly_simulator_poly_method_config` 表创建的数据访问层，提供完整的 CRUD 操作。

## 表结构

```sql
CREATE TABLE `glo_poly_simulator_poly_method_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增Id',
  `method` varchar(50) NOT NULL,
  `req_feild` varchar(50) NOT NULL COMMENT '请求业务参数中集合类型字段',
  `return_feild` varchar(50) NOT NULL COMMENT '对应返回中集合类型字段',
  `feild_map` varchar(1000) DEFAULT NULL COMMENT '请求中需要映射到返回中的字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_method_feilds` (`method`,`req_feild`,`return_feild`) USING BTREE
) ENGINE=InnoDB
```

## 功能方法

### 1. 插入配置
```python
insert(method, req_feild, return_feild, feild_map='')
```
- **参数**:
  - `method`: 方法名
  - `req_feild`: 请求业务参数中集合类型字段
  - `return_feild`: 对应返回中集合类型字段
  - `feild_map`: 字段映射配置（可选）
- **返回**: 执行结果

### 2. 更新配置
```python
update(id, method, req_feild, return_feild, feild_map='')
```
- **参数**:
  - `id`: 配置ID
  - 其他参数同插入方法
- **返回**: 执行结果

### 3. 删除配置
```python
delete(id)
```
- **参数**:
  - `id`: 配置ID
- **返回**: 执行结果

### 4. 查询单个配置
```python
getOne(id)
```
- **参数**:
  - `id`: 配置ID
- **返回**: 配置记录列表

### 5. 根据方法名查询配置
```python
getByMethod(method_name)
```
- **参数**:
  - `method_name`: 方法名
- **返回**: 配置记录列表

### 6. 根据方法名和字段查询配置
```python
getByMethodAndFields(method_name, req_feild, return_feild)
```
- **参数**:
  - `method_name`: 方法名
  - `req_feild`: 请求字段
  - `return_feild`: 返回字段
- **返回**: 配置记录列表

### 7. 分页查询配置
```python
query(reqVars)
```
- **参数**:
  - `reqVars`: 查询参数字典，支持的键：
    - `method`: 方法名过滤
    - `req_feild`: 请求字段过滤
    - `return_feild`: 返回字段过滤
    - `page`: 页码
    - `limit`: 每页记录数
- **返回**: (记录列表, 总记录数) 元组

## 使用示例

```python
from core.repository import polyMethodConfigRepository

# 创建仓储实例
repo = polyMethodConfigRepository()

# 插入新配置
repo.insert(
    method='getOrderList',
    req_feild='orders',
    return_feild='orderlist',
    feild_map='{"platorderno":"order_no","orderstatus":"status"}'
)

# 查询配置
configs = repo.getByMethod('getOrderList')

# 分页查询
reqVars = {'method': 'getOrderList', 'page': 1, 'limit': 10}
configs, total = repo.query(reqVars)
```

## 与现有代码的集成

原有的 `commonExampleRepository.getMethodConfig()` 方法已经更新为使用新的 `polyMethodConfigRepository`，保持向后兼容性。

## 注意事项

1. 表中有唯一约束 `uk_method_feilds`，插入重复的 (method, req_feild, return_feild) 组合会失败
2. `feild_map` 字段通常存储 JSON 格式的字段映射配置
3. 所有数据库操作都包含异常处理和日志记录
